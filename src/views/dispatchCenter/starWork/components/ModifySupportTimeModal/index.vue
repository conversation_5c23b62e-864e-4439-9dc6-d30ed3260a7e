<template>
  <a-modal :visible="visible" title="修改支撑时限" @cancel="handleClose" :footer="null" width="40%">
    <div style="margin-left: 20px;margin-bottom: 20px; font-size: 14px; color: #333; line-height: 1.5;">
      <p style="font-weight: 500; margin-bottom: 8px;">
        能力方支撑时间最少预留3天，请先修改支撑时限后再提交
      </p>
    </div>

    <a-form ref="formRef" :model="formData" :rules="rules" labelAlign="right" :labelCol="{ span: 8 }"
      :wrapperCol="{ span: 15 }" :colon="false">
      <a-row>
        <a-col :span="24">
          <a-form-item label="支撑时限（修改前）">
            <span>{{ originalSupportTime || "无" }}</span>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-config-provider :locale="zhCN">
            <a-form-item label="支撑时限（修改后）" name="supportTime" required>
              <a-date-picker v-model:value="formData.supportTime" placeholder="请选择支撑时限" format="YYYY-MM-DD"
                :showToday="false" :disabled-date="disabledDate" />
            </a-form-item>
          </a-config-provider>
        </a-col>
      </a-row>

      <div class="modal-footer">
        <a-button style="margin-right: 16px;" @click="handleClose">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确认</a-button>
      </div>
    </a-form>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';

export default defineComponent({
  name: 'ModifySupportTimeModal',
  components: {
    'a-config-provider': ConfigProvider
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    originalSupportTime: {
      type: String,
      default: ''
    },
    disabledDate: {
      type: Function,
      default: () => false
    }
  },
  emits: ['update:visible', 'submit'],
  setup(props, { emit }) {
    const formRef = ref();
    const formData = ref({
      supportTime: null
    });

    const rules = {
      supportTime: [
        { required: true, message: '请选择支撑时限', trigger: 'change' }
      ]
    };

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        // 重置表单数据
        formData.value = {
          supportTime: null
        };
      }
    });

    const handleClose = () => {
      emit('update:visible', false);
    };

    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        emit('submit');
      } catch (error) {
        console.log('Validation failed:', error);
      }
    };

    return {
      formRef,
      formData,
      rules,
      zhCN,
      handleClose,
      handleSubmit
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
</style>
